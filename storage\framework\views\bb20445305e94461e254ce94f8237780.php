<?php if (isset($component)) { $__componentOriginal1f7b3c69a858611a4ccc5f2ea9729c12 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal1f7b3c69a858611a4ccc5f2ea9729c12 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'layouts.sidebar','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('sidebar-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        💬 Chat & Komunikasi
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Header Actions -->
            <div class="mb-6">
                <h2 class="text-2xl font-bold text-gray-900">💬 Chat dengan Warga</h2>
                <p class="text-gray-600 mt-1">Pilih warga yang ingin Anda ajak chat</p>
            </div>

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <?php if($users->count() > 0): ?>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            <?php $__currentLoopData = $users; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors cursor-pointer"
                                     onclick="startChat(<?php echo e($user->id); ?>)">
                                    <div class="flex items-center space-x-4">
                                        <!-- User Avatar -->
                                        <div class="flex-shrink-0">
                                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                                                <span class="text-lg font-semibold text-green-600">
                                                    <?php echo e(strtoupper(substr($user->name, 0, 2))); ?>

                                                </span>
                                            </div>
                                        </div>

                                        <!-- User Info -->
                                        <div class="flex-1 min-w-0">
                                            <div class="flex items-center justify-between">
                                                <h3 class="text-lg font-medium text-gray-900 truncate"><?php echo e($user->name); ?></h3>
                                                <?php if($user->unread_count > 0): ?>
                                                    <span class="inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-red-500 rounded-full">
                                                        <?php echo e($user->unread_count > 99 ? '99+' : $user->unread_count); ?>

                                                    </span>
                                                <?php endif; ?>
                                            </div>

                                            <div class="flex items-center space-x-2 mt-1">
                                                <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                                                    <?php echo e($user->role_label); ?>

                                                </span>
                                                <span class="text-xs text-gray-500"><?php echo e($user->rt_rw); ?></span>
                                            </div>

                                            <?php if($user->last_message): ?>
                                                <div class="mt-2">
                                                    <p class="text-sm text-gray-600 truncate">
                                                        <?php if($user->last_message->type === 'file' || $user->last_message->type === 'image'): ?>
                                                            📎 <?php echo e($user->last_message->file_name); ?>

                                                        <?php else: ?>
                                                            <?php echo e($user->last_message->message); ?>

                                                        <?php endif; ?>
                                                    </p>
                                                    <p class="text-xs text-gray-400 mt-1">
                                                        <?php echo e($user->last_message->created_at->diffForHumans()); ?>

                                                    </p>
                                                </div>
                                            <?php else: ?>
                                                <p class="text-sm text-gray-500 mt-2">Belum ada percakapan</p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <!-- Empty State -->
                        <div class="text-center py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">Belum ada warga lain</h3>
                            <p class="mt-1 text-sm text-gray-500">Belum ada warga lain yang terdaftar di sistem.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        function startChat(userId) {
            // Redirect to start private chat route
            window.location.href = `/chat/start-private/${userId}`;
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal1f7b3c69a858611a4ccc5f2ea9729c12)): ?>
<?php $attributes = $__attributesOriginal1f7b3c69a858611a4ccc5f2ea9729c12; ?>
<?php unset($__attributesOriginal1f7b3c69a858611a4ccc5f2ea9729c12); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal1f7b3c69a858611a4ccc5f2ea9729c12)): ?>
<?php $component = $__componentOriginal1f7b3c69a858611a4ccc5f2ea9729c12; ?>
<?php unset($__componentOriginal1f7b3c69a858611a4ccc5f2ea9729c12); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\SIAPSKSKRIPSI\resources\views/chat/index.blade.php ENDPATH**/ ?>