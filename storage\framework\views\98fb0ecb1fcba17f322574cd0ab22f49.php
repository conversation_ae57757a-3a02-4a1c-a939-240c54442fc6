<?php if (isset($component)) { $__componentOriginal1f7b3c69a858611a4ccc5f2ea9729c12 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal1f7b3c69a858611a4ccc5f2ea9729c12 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'layouts.sidebar','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('sidebar-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> Verifikasi Surat <?php $__env->endSlot(); ?>
     <?php $__env->slot('header', null, []); ?> 
        <span><?php echo e(__('Verifikasi Surat')); ?>

        </span>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <!-- Verification Status -->
                    <div class="mb-6">
                        <?php if($valid): ?>
                            <div class="bg-green-50 border border-green-200 rounded-md p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-green-800">
                                            ✅ Surat Valid
                                        </h3>
                                        <div class="mt-2 text-sm text-green-700">
                                            <p><?php echo e($message); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="bg-red-50 border border-red-200 rounded-md p-4">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-red-800">
                                            ❌ Surat Tidak Valid
                                        </h3>
                                        <div class="mt-2 text-sm text-red-700">
                                            <p><?php echo e($message); ?></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <?php if($letterRequest): ?>
                        <!-- Letter Information -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <!-- Letter Details -->
                            <div class="space-y-4">
                                <h4 class="font-medium text-gray-900">Informasi Surat</h4>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Nomor Surat</label>
                                    <p class="mt-1 text-sm text-gray-900"><?php echo e($letterRequest->request_number); ?></p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Jenis Surat</label>
                                    <p class="mt-1 text-sm text-gray-900"><?php echo e($letterRequest->letterType->name); ?></p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Status</label>
                                    <?php
                                        $statusColors = [
                                            'pending_rt' => 'bg-yellow-100 text-yellow-800',
                                            'pending_rw' => 'bg-blue-100 text-blue-800',
                                            'approved_final' => 'bg-green-100 text-green-800',
                                            'rejected_rt' => 'bg-red-100 text-red-800',
                                            'rejected_rw' => 'bg-red-100 text-red-800',
                                        ];
                                    ?>
                                    <span class="mt-1 px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo e($statusColors[$letterRequest->status] ?? 'bg-gray-100 text-gray-800'); ?>">
                                        <?php echo e($letterRequest->status_label); ?>

                                    </span>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Tanggal Pengajuan</label>
                                    <p class="mt-1 text-sm text-gray-900"><?php echo e($letterRequest->created_at->format('d F Y, H:i')); ?> WIB</p>
                                </div>

                                <?php if($letterRequest->final_processed_at): ?>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Tanggal Selesai</label>
                                        <p class="mt-1 text-sm text-gray-900"><?php echo e($letterRequest->final_processed_at->format('d F Y, H:i')); ?> WIB</p>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <!-- Subject Details -->
                            <div class="space-y-4">
                                <h4 class="font-medium text-gray-900">Data Subjek Surat</h4>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Nama Lengkap</label>
                                    <p class="mt-1 text-sm text-gray-900"><?php echo e($letterRequest->subject_name); ?></p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">NIK</label>
                                    <p class="mt-1 text-sm text-gray-900"><?php echo e($letterRequest->subject_details['nik']); ?></p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Hubungan</label>
                                    <p class="mt-1 text-sm text-gray-900"><?php echo e($letterRequest->subject_details['relationship']); ?></p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Alamat</label>
                                    <p class="mt-1 text-sm text-gray-900"><?php echo e($letterRequest->subject_details['address']); ?></p>
                                </div>
                            </div>

                            <!-- Applicant Details -->
                            <div class="space-y-4">
                                <h4 class="font-medium text-gray-900">Data Pengaju</h4>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Nama Lengkap</label>
                                    <p class="mt-1 text-sm text-gray-900"><?php echo e($letterRequest->user->name); ?></p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">NIK</label>
                                    <p class="mt-1 text-sm text-gray-900"><?php echo e($letterRequest->user->nik); ?></p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">Alamat</label>
                                    <p class="mt-1 text-sm text-gray-900"><?php echo e($letterRequest->user->address); ?></p>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700">RT/RW</label>
                                    <p class="mt-1 text-sm text-gray-900"><?php echo e($letterRequest->user->rt_rw); ?></p>
                                </div>

                                <?php if($letterRequest->form_data): ?>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700">Data Tambahan</label>
                                        <div class="mt-1 text-sm text-gray-900">
                                            <?php $__currentLoopData = $letterRequest->form_data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <p><strong><?php echo e(ucwords(str_replace('_', ' ', $key))); ?>:</strong> <?php echo e($value); ?></p>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <!-- Approval History -->
                        <?php if($letterRequest->approvals->count() > 0): ?>
                            <div class="mt-8">
                                <h4 class="font-medium text-gray-900 mb-4">Riwayat Persetujuan</h4>
                                <div class="space-y-4">
                                    <?php $__currentLoopData = $letterRequest->approvals; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $approval): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="border rounded-lg p-4">
                                            <div class="flex items-center justify-between w-full">
                                                <div>
                                                    <p class="text-sm font-medium text-gray-900">
                                                        <?php echo e($approval->approver->name); ?> (<?php echo e(strtoupper($approval->approval_level)); ?>)
                                                    </p>
                                                    <p class="text-sm text-gray-500">
                                                        <?php echo e($approval->processed_at->format('d F Y, H:i')); ?> WIB
                                                    </p>
                                                </div>
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full <?php echo e($approval->status === 'approved' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'); ?>">
                                                    <?php echo e($approval->status === 'approved' ? 'Disetujui' : 'Ditolak'); ?>

                                                </span>
                                            </div>
                                            <?php if($approval->notes): ?>
                                                <p class="mt-2 text-sm text-gray-600"><?php echo e($approval->notes); ?></p>
                                            <?php endif; ?>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>

                    <!-- Actions -->
                    <div class="mt-8 flex items-center justify-between">
                        <a href="<?php echo e(route('qr-verification.scan')); ?>" 
                           class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Scan QR Code Lain
                        </a>
                        
                        <a href="<?php echo e(route('dashboard')); ?>" 
                           class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                            Kembali ke Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal1f7b3c69a858611a4ccc5f2ea9729c12)): ?>
<?php $attributes = $__attributesOriginal1f7b3c69a858611a4ccc5f2ea9729c12; ?>
<?php unset($__attributesOriginal1f7b3c69a858611a4ccc5f2ea9729c12); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal1f7b3c69a858611a4ccc5f2ea9729c12)): ?>
<?php $component = $__componentOriginal1f7b3c69a858611a4ccc5f2ea9729c12; ?>
<?php unset($__componentOriginal1f7b3c69a858611a4ccc5f2ea9729c12); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\SIAPSKSKRIPSI\resources\views/qr-verification/result.blade.php ENDPATH**/ ?>