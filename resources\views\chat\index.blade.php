<x-sidebar-layout>
    <x-slot name="header">
        💬 <PERSON><PERSON> & <PERSON>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Header Actions -->
            <div class="mb-6 flex justify-between items-center">
                <h2 class="text-2xl font-bold text-gray-900">Daftar Chat</h2>
                <a href="{{ route('chat.create') }}" 
                   class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    <span>Chat Baru</span>
                </a>
            </div>

            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    @if($chats->count() > 0)
                        <div class="space-y-4">
                            @foreach($chats as $chat)
                                <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors">
                                    <a href="{{ route('chat.show', $chat) }}" class="block">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-4">
                                                <!-- Chat Icon -->
                                                <div class="flex-shrink-0">
                                                    @if($chat->type === 'private')
                                                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                                                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                                            </svg>
                                                        </div>
                                                    @elseif($chat->type === 'group')
                                                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                                                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                                            </svg>
                                                        </div>
                                                    @else
                                                        <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                                                            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
                                                            </svg>
                                                        </div>
                                                    @endif
                                                </div>

                                                <!-- Chat Info -->
                                                <div class="flex-1 min-w-0">
                                                    <div class="flex items-center space-x-2">
                                                        <h3 class="text-lg font-medium text-gray-900 truncate">{{ $chat->title }}</h3>
                                                        @if($chat->type === 'private')
                                                            <span class="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">Private</span>
                                                        @elseif($chat->type === 'group')
                                                            <span class="px-2 py-1 text-xs bg-green-100 text-green-800 rounded-full">Group</span>
                                                        @else
                                                            <span class="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">Pengumuman</span>
                                                        @endif
                                                    </div>
                                                    
                                                    @if($chat->latestMessage)
                                                        <div class="mt-1">
                                                            <p class="text-sm text-gray-600 truncate">
                                                                <span class="font-medium">{{ $chat->latestMessage->user->name }}:</span>
                                                                @if($chat->latestMessage->type === 'file' || $chat->latestMessage->type === 'image')
                                                                    📎 {{ $chat->latestMessage->file_name }}
                                                                @else
                                                                    {{ $chat->latestMessage->message }}
                                                                @endif
                                                            </p>
                                                            <p class="text-xs text-gray-400 mt-1">
                                                                {{ $chat->latestMessage->created_at->diffForHumans() }}
                                                            </p>
                                                        </div>
                                                    @else
                                                        <p class="text-sm text-gray-500 mt-1">Belum ada pesan</p>
                                                    @endif
                                                </div>
                                            </div>

                                            <!-- Unread Badge -->
                                            @if($chat->unread_count > 0)
                                                <div class="flex-shrink-0">
                                                    <span class="inline-flex items-center justify-center w-6 h-6 text-xs font-bold text-white bg-red-500 rounded-full">
                                                        {{ $chat->unread_count > 99 ? '99+' : $chat->unread_count }}
                                                    </span>
                                                </div>
                                            @endif
                                        </div>
                                    </a>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <!-- Empty State -->
                        <div class="text-center py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">Belum ada chat</h3>
                            <p class="mt-1 text-sm text-gray-500">Mulai percakapan dengan membuat chat baru.</p>
                            <div class="mt-6">
                                <a href="{{ route('chat.create') }}" 
                                   class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                    </svg>
                                    Buat Chat Baru
                                </a>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-sidebar-layout>
