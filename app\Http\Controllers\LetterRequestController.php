<?php

namespace App\Http\Controllers;

use App\Models\LetterRequest;
use App\Models\LetterType;
use App\Services\QrCodeService;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class LetterRequestController extends Controller
{
    // Middleware will be applied via routes

    public function index()
    {
        $requests = auth()->user()->letterRequests()
            ->with(['letterType', 'subject'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('letter-requests.index', compact('requests'));
    }

    public function create()
    {
        $letterTypes = LetterType::active()->get();
        $familyMembers = auth()->user()->approvedFamilyMembers()->get();

        return view('letter-requests.create', compact('letterTypes', 'familyMembers'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'letter_type_id' => 'required|exists:letter_types,id',
            'subject_type' => 'required|in:self,family_member',
            'subject_id' => 'nullable|exists:family_members,id',
            'form_data' => 'required|array',
        ]);

        // Validate subject_id is required when subject_type is family_member
        if ($request->subject_type === 'family_member') {
            $request->validate([
                'subject_id' => 'required|exists:family_members,id'
            ]);

            // Ensure the family member belongs to the authenticated user and is approved
            $familyMember = auth()->user()->approvedFamilyMembers()->find($request->subject_id);
            if (!$familyMember) {
                return back()->withErrors(['subject_id' => 'Anggota keluarga tidak valid atau belum disetujui.']);
            }
        }

        $letterType = LetterType::findOrFail($request->letter_type_id);

        // Validate required fields based on letter type
        if ($letterType->required_fields) {
            foreach ($letterType->required_fields as $field => $type) {
                $request->validate([
                    "form_data.{$field}" => 'required'
                ]);
            }
        }

        // Generate request number
        $requestNumber = 'REQ-' . date('Ymd') . '-' . str_pad(
            LetterRequest::whereDate('created_at', today())->count() + 1,
            4,
            '0',
            STR_PAD_LEFT
        );

        LetterRequest::create([
            'request_number' => $requestNumber,
            'user_id' => auth()->id(),
            'subject_type' => $request->subject_type,
            'subject_id' => $request->subject_type === 'family_member' ? $request->subject_id : null,
            'letter_type_id' => $request->letter_type_id,
            'form_data' => $request->form_data,
            'status' => 'pending_rt',
        ]);

        return redirect()->route('letter-requests.index')
            ->with('success', 'Pengajuan surat berhasil dibuat dengan nomor: ' . $requestNumber);
    }

    public function show(LetterRequest $letterRequest)
    {
        // Simple ownership check - allow user to view their own requests
        $currentUserId = auth()->id();
        $letterUserId = $letterRequest->user_id;

        // Multiple comparison methods to ensure it works
        $isOwner = false;
        if ($letterUserId == $currentUserId) {
            $isOwner = true;
        } elseif ((string)$letterUserId === (string)$currentUserId) {
            $isOwner = true;
        } elseif ((int)$letterUserId === (int)$currentUserId) {
            $isOwner = true;
        }

        if (!$isOwner) {
            abort(403, 'Anda tidak memiliki akses untuk melihat surat ini. Letter User ID: ' . $letterUserId . ', Current User ID: ' . $currentUserId);
        }

        $letterRequest->load(['letterType', 'approvals.approver', 'subject']);

        return view('letter-requests.show', compact('letterRequest'));
    }

    public function download(LetterRequest $letterRequest)
    {
        // Simple ownership check
        $currentUserId = auth()->id();
        $letterUserId = $letterRequest->user_id;

        // Multiple comparison methods to ensure it works
        $isOwner = false;
        if ($letterUserId == $currentUserId) {
            $isOwner = true;
        } elseif ((string)$letterUserId === (string)$currentUserId) {
            $isOwner = true;
        } elseif ((int)$letterUserId === (int)$currentUserId) {
            $isOwner = true;
        }

        if (!$isOwner) {
            abort(403, 'Anda tidak memiliki akses untuk mendownload surat ini. Letter User ID: ' . $letterUserId . ', Current User ID: ' . $currentUserId);
        }

        if (!$letterRequest->isApproved()) {
            abort(403, 'Surat belum disetujui dan tidak dapat didownload. Status: ' . $letterRequest->status);
        }

        if (!$letterRequest->letter_file) {
            return redirect()->back()->with('error', 'File surat belum tersedia.');
        }

        $filePath = storage_path('app/public/' . $letterRequest->letter_file);
        if (!file_exists($filePath)) {
            return redirect()->back()->with('error', 'File surat tidak ditemukan di server.');
        }

        return response()->download(
            $filePath,
            'Surat_' . $letterRequest->request_number . '.pdf'
        );
    }
}
