<?php if (isset($component)) { $__componentOriginal1f7b3c69a858611a4ccc5f2ea9729c12 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal1f7b3c69a858611a4ccc5f2ea9729c12 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'layouts.sidebar','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('sidebar-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        💬 <?php echo e($chat->getTitleForUser(auth()->id())); ?>

     <?php $__env->endSlot(); ?>

    <div class="py-6">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <!-- Chat Header -->
                <div class="bg-green-600 px-6 py-4 flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <a href="<?php echo e(route('chat.index')); ?>" class="text-white hover:text-green-200 transition-colors">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                            </svg>
                        </a>
                        <div>
                            <h3 class="text-lg font-semibold text-white"><?php echo e($chat->getTitleForUser(auth()->id())); ?></h3>
                            <p class="text-green-100 text-sm">
                                <?php if($chat->type === 'private'): ?>
                                    Chat Private
                                <?php elseif($chat->type === 'group'): ?>
                                    <?php echo e($participants->count()); ?> peserta
                                <?php else: ?>
                                    Pengumuman
                                <?php endif; ?>
                            </p>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <?php if($chat->type === 'group' && ($chat->created_by === auth()->id() || auth()->user()->role === 'admin')): ?>
                            <button onclick="openParticipantsModal()" class="text-white hover:text-green-200 transition-colors">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                </svg>
                            </button>
                        <?php endif; ?>
                        
                        <button onclick="openInfoModal()" class="text-white hover:text-green-200 transition-colors">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- Messages Container -->
                <div class="h-96 overflow-y-auto p-6" id="messagesContainer">
                    <?php $__empty_1 = true; $__currentLoopData = $messages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $message): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <?php
                            $currentUserName = auth()->user()->name;
                            $messageUserName = $message->user->name;
                            $isMyMessage = ($messageUserName === $currentUserName);
                        ?>

                        <?php if($isMyMessage): ?>
                            <!-- My Messages (Right Side) -->
                            <div class="w-full mb-4" style="display: flex; justify-content: flex-end;">
                                <div class="max-w-xs lg:max-w-md" style="margin-left: auto;">
                                    <div class="px-4 py-2 rounded-lg bg-green-600 text-white rounded-br-none">
                                        <?php if($message->type === 'image'): ?>
                                            <img src="<?php echo e($message->getFileUrl()); ?>" alt="<?php echo e($message->file_name); ?>" class="max-w-full h-auto rounded mb-2">
                                        <?php elseif($message->type === 'file'): ?>
                                            <div class="flex items-center space-x-2 mb-2">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                                </svg>
                                                <a href="<?php echo e(route('chat.download-file', $message)); ?>" class="underline text-white"><?php echo e($message->file_name); ?></a>
                                            </div>
                                        <?php endif; ?>

                                        <?php if($message->message): ?>
                                            <p class="text-sm"><?php echo e($message->message); ?></p>
                                        <?php endif; ?>
                                    </div>

                                    <div class="text-xs text-gray-400 mt-1 text-right">
                                        <?php echo e($message->created_at->format('H:i')); ?>

                                    </div>
                                </div>
                            </div>
                        <?php else: ?>
                            <!-- Other Messages (Left Side) -->
                            <div class="w-full mb-4" style="display: flex; justify-content: flex-start;">
                                <div class="max-w-xs lg:max-w-md" style="margin-right: auto;">
                                    <div class="flex items-center space-x-2 mb-1">
                                        <div class="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center">
                                            <span class="text-xs font-medium text-gray-600">
                                                <?php echo e(strtoupper(substr($message->user->name, 0, 1))); ?>

                                            </span>
                                        </div>
                                        <span class="text-xs text-gray-500"><?php echo e($message->user->name); ?></span>
                                    </div>

                                    <div class="px-4 py-2 rounded-lg bg-gray-200 text-gray-900 rounded-bl-none">
                                        <?php if($message->type === 'image'): ?>
                                            <img src="<?php echo e($message->getFileUrl()); ?>" alt="<?php echo e($message->file_name); ?>" class="max-w-full h-auto rounded mb-2">
                                        <?php elseif($message->type === 'file'): ?>
                                            <div class="flex items-center space-x-2 mb-2">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                                </svg>
                                                <a href="<?php echo e(route('chat.download-file', $message)); ?>" class="underline text-gray-700"><?php echo e($message->file_name); ?></a>
                                            </div>
                                        <?php endif; ?>

                                        <?php if($message->message): ?>
                                            <p class="text-sm"><?php echo e($message->message); ?></p>
                                        <?php endif; ?>
                                    </div>

                                    <div class="text-xs text-gray-400 mt-1 text-left">
                                        <?php echo e($message->created_at->format('H:i')); ?>

                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="text-center py-8 text-gray-500">
                            <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"/>
                            </svg>
                            <p>Belum ada pesan. Mulai percakapan!</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Message Input -->
                <div class="border-t border-gray-200 p-4">
                    <form id="messageForm" enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <div class="flex items-end space-x-3">
                            <div class="flex-1">
                                <textarea name="message" 
                                         id="messageInput"
                                         rows="1" 
                                         class="w-full border-gray-300 rounded-lg resize-none focus:ring-green-500 focus:border-green-500"
                                         placeholder="Ketik pesan..."
                                         onkeydown="handleKeyDown(event)"></textarea>
                            </div>
                            
                            <div class="flex items-center space-x-2">
                                <label for="fileInput" class="cursor-pointer text-gray-500 hover:text-green-600 transition-colors">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"/>
                                    </svg>
                                </label>
                                <input type="file" id="fileInput" name="file" class="hidden" onchange="handleFileSelect(event)">
                                
                                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white p-2 rounded-lg transition-colors">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                        
                        <div id="filePreview" class="mt-2 hidden">
                            <div class="flex items-center space-x-2 p-2 bg-gray-100 rounded">
                                <span id="fileName" class="text-sm text-gray-700"></span>
                                <button type="button" onclick="removeFile()" class="text-red-500 hover:text-red-700">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Info Modal -->
    <div id="infoModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-md w-full p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold">Info Chat</h3>
                    <button onclick="closeInfoModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                        </svg>
                    </button>
                </div>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Judul</label>
                        <p class="text-sm text-gray-900"><?php echo e($chat->title); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Jenis</label>
                        <p class="text-sm text-gray-900">
                            <?php if($chat->type === 'private'): ?>
                                Chat Private
                            <?php elseif($chat->type === 'group'): ?>
                                Group Chat
                            <?php else: ?>
                                Pengumuman
                            <?php endif; ?>
                        </p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Dibuat oleh</label>
                        <p class="text-sm text-gray-900"><?php echo e($chat->creator->name); ?></p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Peserta (<?php echo e($participants->count()); ?>)</label>
                        <div class="mt-2 space-y-2">
                            <?php $__currentLoopData = $participants; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $participant): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-gray-300 rounded-full flex items-center justify-center">
                                        <span class="text-xs font-medium text-gray-600">
                                            <?php echo e(strtoupper(substr($participant->name, 0, 1))); ?>

                                        </span>
                                    </div>
                                    <span class="text-sm text-gray-900"><?php echo e($participant->name); ?></span>
                                    <?php if($participant->id === $chat->created_by): ?>
                                        <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">Creator</span>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6 flex justify-end space-x-3">
                    <?php if($chat->created_by !== auth()->id() && $chat->type !== 'announcement'): ?>
                        <form method="POST" action="<?php echo e(route('chat.leave', $chat)); ?>">
                            <?php echo csrf_field(); ?>
                            <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm">
                                Keluar dari Chat
                            </button>
                        </form>
                    <?php endif; ?>
                    <button onclick="closeInfoModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-lg text-sm">
                        Tutup
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        let messagesContainer = document.getElementById('messagesContainer');
        
        // Scroll to bottom on load
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        // Handle form submission
        document.getElementById('messageForm').addEventListener('submit', function(e) {
            e.preventDefault();
            sendMessage();
        });

        function handleKeyDown(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        function sendMessage() {
            const form = document.getElementById('messageForm');
            const formData = new FormData(form);
            const messageInput = document.getElementById('messageInput');
            
            if (!formData.get('message').trim() && !formData.get('file')) {
                return;
            }

            fetch('<?php echo e(route("chat.send-message", $chat)); ?>', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    messageInput.value = '';
                    removeFile();
                    loadMessages();
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        }

        function loadMessages() {
            fetch('<?php echo e(route("chat.get-messages", $chat)); ?>')
                .then(response => response.json())
                .then(messages => {
                    updateMessagesDisplay(messages);
                    messagesContainer.scrollTop = messagesContainer.scrollHeight;
                });
        }

        function updateMessagesDisplay(messages) {
            // This would update the messages display
            // For now, we'll just reload the page
            location.reload();
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                document.getElementById('fileName').textContent = file.name;
                document.getElementById('filePreview').classList.remove('hidden');
            }
        }

        function removeFile() {
            document.getElementById('fileInput').value = '';
            document.getElementById('filePreview').classList.add('hidden');
        }

        function openInfoModal() {
            document.getElementById('infoModal').classList.remove('hidden');
        }

        function closeInfoModal() {
            document.getElementById('infoModal').classList.add('hidden');
        }

        // Auto-refresh messages every 5 seconds
        setInterval(loadMessages, 5000);
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal1f7b3c69a858611a4ccc5f2ea9729c12)): ?>
<?php $attributes = $__attributesOriginal1f7b3c69a858611a4ccc5f2ea9729c12; ?>
<?php unset($__attributesOriginal1f7b3c69a858611a4ccc5f2ea9729c12); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal1f7b3c69a858611a4ccc5f2ea9729c12)): ?>
<?php $component = $__componentOriginal1f7b3c69a858611a4ccc5f2ea9729c12; ?>
<?php unset($__componentOriginal1f7b3c69a858611a4ccc5f2ea9729c12); ?>
<?php endif; ?>
<?php /**PATH C:\Users\<USER>\Documents\GitHub\SIAPSKSKRIPSI\resources\views/chat/show.blade.php ENDPATH**/ ?>